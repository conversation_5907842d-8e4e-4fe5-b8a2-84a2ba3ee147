import {chromium} from 'playwright';

const browser = await chromium.launch({ headless: false });
const context = await browser.newContext();
const page = await context.newPage();

await page.goto('https://news.ycombinator.com', { waitUntil: 'domcontentloaded' });

await page.waitForSelector('.athing');

const exists = await page.waitForSelector('.athing');

if (!exists) {
    console.log('No news');
} else {
   const news = await.page.$$eval('.athing', (news) =>
    news.map((new) => {
        return {hola: 'mundo'}
    })
   );
   await browser.close();
}

